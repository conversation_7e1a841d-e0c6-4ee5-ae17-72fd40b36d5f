// pages/complaint/list.js
import employeeSuggestionApi from '../../api/modules/employeeSuggestion';
import Session from '../../common/Session';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    userInfo: null,
    complaintList: [],
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 10,
    
    // 筛选条件
    currentTab: 'all',
    tabs: [
      { key: 'all', label: '全部建议' },
      { key: 'platform', label: '平台建议' },
      { key: 'service', label: '服务建议' },
      { key: 'workflow', label: '流程建议' }
    ],
    
    // 状态映射
    statusMap: {
      pending: { text: '待处理', color: '#ff9500' },
      processing: { text: '处理中', color: '#007aff' },
      resolved: { text: '已解决', color: '#34c759' },
      closed: { text: '已关闭', color: '#8e8e93' }
    },
    
    // 分类映射
    categoryMap: {
      suggestion: '建议'
    },

    subCategoryMap: {
      platform: '平台建议',
      service: '服务建议',
      workflow: '流程建议'
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    const userInfo = Session.getUser();
    this.setData({ userInfo });
    this.loadSuggestionList();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 刷新列表
    this.refreshList();
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.refreshList();
  },

  /**
   * 上拉加载更多
   */
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMore();
    }
  },

  /**
   * 切换标签
   */
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab;
    if (tab === this.data.currentTab) return;
    
    this.setData({ currentTab: tab });
    this.refreshList();
  },

  /**
   * 刷新列表
   */
  refreshList() {
    this.setData({
      complaintList: [],
      page: 1,
      hasMore: true
    });
    this.loadSuggestionList();
  },

  /**
   * 加载更多
   */
  loadMore() {
    this.setData({
      page: this.data.page + 1
    });
    this.loadSuggestionList();
  },

  /**
   * 加载建议列表
   */
  async loadSuggestionList() {
    const { userInfo, page, pageSize, currentTab, complaintList } = this.data;

    if (!userInfo) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    this.setData({ loading: true });

    try {
      const params = {
        current: page,
        pageSize
      };

      // 添加子分类筛选
      if (currentTab !== 'all') {
        params.subCategory = currentTab;
      }

      const result = await employeeSuggestionApi.list(userInfo.id, params);

      if (result && result.list) {
        const newList = page === 1 ? result.list : [...complaintList, ...result.list];
        this.setData({
          complaintList: newList,
          hasMore: result.list.length === pageSize
        });
      } else {
        this.setData({
          complaintList: page === 1 ? [] : complaintList,
          hasMore: false
        });
      }
    } catch (error) {
      console.error('加载建议列表失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
      wx.stopPullDownRefresh();
    }
  },

  /**
   * 查看详情
   */
  viewDetail(e) {
    const complaint = e.currentTarget.dataset.complaint;
    wx.navigateTo({
      url: `/pages/complaint/detail?id=${complaint.id}`
    });
  },

  /**
   * 编辑建议
   */
  editSuggestion(e) {
    const suggestion = e.currentTarget.dataset.complaint;

    // 只有待处理状态的建议才能编辑
    if (suggestion.status !== 'pending') {
      wx.showToast({
        title: '只有待处理状态的建议才能编辑',
        icon: 'none'
      });
      return;
    }

    // 跳转到创建页面，传递编辑参数
    wx.navigateTo({
      url: `/pages/complaint/index?mode=edit&id=${suggestion.id}`
    });
  },

  /**
   * 删除建议
   */
  deleteSuggestion(e) {
    const suggestion = e.currentTarget.dataset.complaint;

    // 只有待处理状态的建议才能删除
    if (suggestion.status !== 'pending') {
      wx.showToast({
        title: '只有待处理状态的建议才能删除',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认删除',
      content: '删除后无法恢复，确定要删除这条建议吗？',
      success: async (res) => {
        if (res.confirm) {
          await this.performDelete(suggestion.id);
        }
      }
    });
  },

  /**
   * 执行删除
   */
  async performDelete(suggestionId) {
    try {
      wx.showLoading({ title: '删除中...' });

      const { userInfo } = this.data;
      await employeeSuggestionApi.delete(userInfo.id, suggestionId);

      wx.showToast({
        title: '删除成功',
        icon: 'success'
      });

      // 刷新列表
      this.refreshList();
    } catch (error) {
      console.error('删除建议失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 创建新建议
   */
  createSuggestion() {
    wx.navigateTo({
      url: '/pages/complaint/index'
    });
  },

  /**
   * 格式化时间
   */
  formatTime(timestamp) {
    const date = this.safeParseDateForIOS(timestamp);
    if (!date) return timestamp; // 如果解析失败，返回原值

    const now = new Date();
    const diff = now - date;

    if (diff < 60000) { // 1分钟内
      return '刚刚';
    } else if (diff < 3600000) { // 1小时内
      return Math.floor(diff / 60000) + '分钟前';
    } else if (diff < 86400000) { // 1天内
      return Math.floor(diff / 3600000) + '小时前';
    } else if (diff < 604800000) { // 1周内
      return Math.floor(diff / 86400000) + '天前';
    } else {
      return date.toLocaleDateString();
    }
  },

  // 安全的日期解析函数，兼容 iOS
  safeParseDateForIOS(dateInput) {
    if (!dateInput) return null;

    if (dateInput instanceof Date) {
      return isNaN(dateInput.getTime()) ? null : dateInput;
    }

    if (typeof dateInput === 'number') {
      const date = new Date(dateInput);
      return isNaN(date.getTime()) ? null : date;
    }

    if (typeof dateInput === 'string') {
      let dateStr = dateInput.trim();

      // 处理常见的不兼容格式
      if (/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(dateStr)) {
        dateStr = dateStr.replace(/-/g, '/');
      } else if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
        dateStr = dateStr.replace(/-/g, '/');
      }

      const date = new Date(dateStr);
      return isNaN(date.getTime()) ? null : date;
    }

    return null;
  }
});
