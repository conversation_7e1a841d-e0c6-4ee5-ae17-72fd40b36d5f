import { formatNormalDate } from '../../utils/util';
import orderApi from '../../../api/modules/order';
import reviewApi from '../../../api/modules/review';
import specialNoteApi from '../../../api/modules/specialNote';
import serviceDurationApi from '../../../api/modules/serviceDuration';
import Session from '../../../common/Session';
import AddressUtils from '../../../utils/AddressUtils.js';

Page({
  data: {
    orderDetail: {}, // 订单
    // 时间选择器相关
    showTimePicker: false, // 是否显示时间选择器
    selectedTime: '', // 选择的时间
    userInfo: null, // 用户信息
    // 追加服务相关
    pendingAdditionalServices: [], // 待确认的追加服务列表
    allAdditionalServices: [], // 所有追加服务列表
    confirmedAdditionalServiceOrders: [], // 已确认的追加服务订单（不包括主订单增项）
    showRejectModal: false, // 是否显示拒绝原因输入框
    rejectReason: '', // 拒绝原因
    currentAdditionalService: null, // 当前操作的追加服务
    // 特殊情况说明相关
    specialNoteData: null, // 特殊情况说明数据
    showSpecialNote: false, // 是否显示特殊情况说明组件
    specialNoteReadonly: false, // 特殊情况说明是否为只读模式

    // 地址编辑器相关
    showAddressEditor: false, // 是否显示地址编辑器

    // 请求状态控制（防止重复请求）
    loadingStates: {
      orderDetail: false,
      serviceDurationRecords: false,
      additionalServices: false,
      orderServiceStatus: false,
    },

    // 更多操作菜单相关
    showMoreActions: false, // 是否显示更多操作菜单

    // 评价相关
    reviewData: null, // 评价数据
    hasReview: false, // 是否有评价
    reviewLoading: false, // 评价加载状态

    // 服务时长统计相关
    serviceDurationRecords: [], // 服务时长记录列表
    mainServiceRecords: [], // 主服务时长记录
    additionalServiceRecords: [], // 增项服务时长记录
    serviceDurationStatistics: {}, // 服务时长统计信息
    showServiceDuration: true, // 是否显示服务时长统计（默认展开）
    serviceDurationTimer: null, // 服务时长计时器

    // 服务照片相关
    servicePhotos: null, // 服务照片数据
    hasServicePhotos: false, // 是否有服务照片
    servicePhotosLoading: false, // 服务照片加载状态
  },

  onLoad(options) {
    // 获取用户信息
    const userInfo = Session.getUser();
    this.setData({ userInfo });
    this.loadOrderDetail(options.orderId);
  },

  onShow() {
    // 页面显示时刷新数据（从支付页面返回时会触发）
    if (this.data.orderDetail && this.data.orderDetail.id) {
      // 只调用一次 loadOrderDetail，它会根据状态自动加载相应的数据
      this.loadOrderDetail(this.data.orderDetail.id);
    }

    // 启动实时计时器
    this.startRealtimeTimer();
  },

  onHide() {
    // 页面隐藏时停止计时器
    this.stopRealtimeTimer();
  },

  // 设置订单详情数据并优化数据加载
  async setOrderDetail(info) {
    // 使用从订单列表传递过来的追加服务信息
    const pendingAdditionalServices = info.pendingAdditionalServices || [];

    const orderDetailData = {
      ...info,
      additionalServices: info.orderDetails?.flatMap(detail => detail.additionalServices?.map(v => v.name) || []) || [],
      petName: info.orderDetails?.map(item => item.petName)[0] || '',
      serviceTime: info.serviceTime ? formatNormalDate(info.serviceTime) : null,
      // 下单时间
      orderTime: info.orderTime ? formatNormalDate(info.orderTime) : null,
      createdAt: info.createdAt ? formatNormalDate(info.createdAt) : null,
      originalPrice: info.originalPrice, // 原价
      totalFee: info.totalFee, // 实付金额
      // 确保userRemark字段被保留
      userRemark: info.userRemark,
    };

    this.setData({
      orderDetail: orderDetailData,
      pendingAdditionalServices: pendingAdditionalServices.map(item => ({
        ...item,
        createdAt: item.createdAt ? formatNormalDate(item.createdAt) : null,
      })),
    });

    // 优化数据加载：使用Promise.all并行加载无依赖的数据
    const orderId = info.id;
    const orderDetailId = info.orderDetails?.[0]?.id;

    try {
      // 并行加载无依赖关系的数据
      const parallelPromises = [];

      // 根据订单状态加载相应的数据
      const status = info.status;

      // 特殊情况说明：服务中、已完成、已评价、已取消、已退款状态显示
      if (['服务中', '已完成', '已评价', '已取消', '已退款'].includes(status)) {
        parallelPromises.push(this.loadSpecialNote(orderId));
      }

      // 评价数据：仅已评价状态显示
      if (status === '已评价') {
        parallelPromises.push(this.loadReviewData(orderId));
      }

      // 服务照片：服务中、已完成、已评价状态显示
      if (['服务中', '已完成', '已评价'].includes(status)) {
        parallelPromises.push(this.loadServicePhotos(orderId));
      }

      // 追加服务：服务中、已完成、已评价、已取消、已退款状态显示
      if (orderDetailId && ['服务中', '已完成', '已评价', '已取消', '已退款'].includes(status)) {
        parallelPromises.push(this.loadAllAdditionalServices(orderDetailId));
      }

      // 并行执行无依赖的数据加载
      await Promise.all(parallelPromises);

      // 链式加载有依赖关系的服务时长数据
      // 服务时长统计：服务中、已完成、已评价、已取消、已退款状态显示
      if (['服务中', '已完成', '已评价', '已取消', '已退款'].includes(status)) {
        if (status === '服务中') {
          // 服务中状态：先加载订单服务状态，再加载详细记录
          await this.loadOrderServiceStatus(orderId);
          await this.loadServiceDurationRecords(orderId);
          this.startServiceTimer();
        } else {
          // 其他状态：只加载服务时长记录
          await this.loadServiceDurationRecords(orderId);
        }
      }
    } catch (error) {
      console.error('数据加载过程中出现错误:', error);
    }
  },

  // 加载所有追加服务
  async loadAllAdditionalServices(orderDetailId) {
    // 防止重复请求
    if (this.data.loadingStates.additionalServices) {
      return;
    }

    try {
      // 设置加载状态
      this.setData({
        'loadingStates.additionalServices': true,
      });

      const res = await orderApi.getAdditionalServices(orderDetailId);

      if (res && typeof res === 'object') {
        const { originalAdditionalServices = [], additionalServiceOrders = [], summary = {} } = res;

        // 处理主订单的增项服务（已包含在主订单中，无需额外支付）
        const formattedOriginalServices = originalAdditionalServices.map(item => ({
          ...item,
          type: 'original', // 标记为主订单增项服务
          status: 'confirmed', // 主订单增项服务默认已确认
          statusText: '已确认',
          serviceName: item.name,
          servicePrice: item.price,
          needDurationTracking: item.needDurationTracking, // 使用后端返回的字段
          additionalServiceId: item.id, // 用于时长统计API
          orderDetailId: orderDetailId, // 主订单详情ID，用于API调用
          createdAt: null,
          confirmTime: null,
        }));

        // 处理追加服务订单（需要确认和支付流程）
        const formattedAdditionalOrders = additionalServiceOrders.map(item => {
          // 优先使用后端返回的 needDurationTracking 字段
          // 如果后端没有返回该字段，则使用前端逻辑计算
          let needDurationTracking = item.needDurationTracking;

          if (needDurationTracking === undefined) {
            // 后端没有返回该字段时，使用前端逻辑计算
            const orderStatus = this.data.orderDetail?.status;

            if (orderStatus === '服务中') {
              // 服务中状态：只有已付款的才需要时长统计
              needDurationTracking = item.status === 'paid';
            } else if (['已完成', '已评价'].includes(orderStatus)) {
              // 已完成状态：所有已确认的追加服务都显示时长信息
              needDurationTracking = ['confirmed', 'paid', 'completed'].includes(item.status);
            } else {
              // 其他状态：按原逻辑处理
              needDurationTracking = item.status === 'paid';
            }
          }

          return {
            ...item,
            type: 'additional', // 标记为追加服务订单
            createdAt: item.createdAt ? formatNormalDate(item.createdAt) : null,
            confirmTime: item.confirmTime ? formatNormalDate(item.confirmTime) : null,
            statusText: this.getAdditionalServiceStatusTextByStatus(item.status),
            // 从details中提取服务信息
            serviceName: item.details?.[0]?.serviceName || '未知服务',
            servicePrice: item.totalFee || item.originalPrice,
            needDurationTracking: needDurationTracking,
            additionalServiceId: item.details?.[0]?.serviceId || item.details?.[0]?.additionalServiceId, // 用于时长统计API
            // 新增：操作权限控制
            canStartService: item.status === 'paid', // 只有已付款的才能开始服务
            canDelete: item.status === 'pending_payment' || item.status === 'confirmed', // 未付款的可以删除
          };
        });

        // 合并所有增项服务
        const allServices = [...formattedOriginalServices, ...formattedAdditionalOrders];

        // 分离待确认和其他状态的追加服务（只有追加服务订单才有待确认状态）
        const pendingServices = formattedAdditionalOrders.filter(item => item.status === 'pending_confirm');
        const confirmedServices = allServices.filter(
          item => item.type === 'original' || (item.type === 'additional' && item.status !== 'pending_confirm')
        );

        // 只获取已确认的追加服务订单（不包括主订单增项）
        const confirmedAdditionalServiceOrders = formattedAdditionalOrders.filter(
          item => item.status !== 'pending_confirm'
        );

        this.setData({
          pendingAdditionalServices: pendingServices, // 待确认的追加服务订单
          allAdditionalServices: confirmedServices, // 所有已确认的服务（包括主订单增项和已确认的追加服务）
          confirmedAdditionalServiceOrders: confirmedAdditionalServiceOrders, // 只包含已确认的追加服务订单（不包括主订单增项）
          originalAdditionalServices: formattedOriginalServices, // 主订单增项服务
          additionalServiceSummary: summary, // 统计信息
        });



        // 增项服务加载完成，不在这里计算显示数据
        // 等待服务时长记录加载完成后统一计算
      } else {
        this.setData({
          pendingAdditionalServices: [],
          allAdditionalServices: [],
          originalAdditionalServices: [],
          additionalServiceSummary: {},
        });
      }

      // 重置加载状态
      this.setData({
        'loadingStates.additionalServices': false,
      });
    } catch (error) {
      console.error('加载追加服务失败:', error);
      this.setData({
        pendingAdditionalServices: [],
        allAdditionalServices: [],
        confirmedAdditionalServiceOrders: [],
        originalAdditionalServices: [],
        additionalServiceSummary: {},
        'loadingStates.additionalServices': false,
      });
    }
  },

  // 获取追加服务状态文字（根据status字段）
  getAdditionalServiceStatusTextByStatus(status) {
    const statusMap = {
      pending_confirm: '待确认',
      confirmed: '已确认，待付款',
      rejected: '已拒绝',
      pending_payment: '待付款',
      paid: '已付款',
      completed: '已完成',
      cancelled: '已取消',
      refunding: '退款中',
      refunded: '已退款',
    };
    return statusMap[status] || status;
  },

  // 检查追加服务是否已付款
  isAdditionalServicePaid(service) {
    // 主订单增项服务默认已付款
    if (service.type === 'original') {
      return true;
    }
    // 追加服务订单需要检查状态
    // paid: 已付款, completed: 已完成 都认为是已付款状态
    return service.status === 'paid' || service.status === 'completed';
  },

  // 检查是否可以删除追加服务（仅未付款的追加服务可删除）
  canDeleteAdditionalService(service) {
    // 主订单增项服务不能删除
    if (service.type === 'original') {
      return false;
    }
    // 只有未付款的追加服务可以删除
    return service.status === 'pending_payment' || service.status === 'confirmed';
  },

  // 加载评价数据
  async loadReviewData(orderId) {
    this.setData({ reviewLoading: true });

    try {
      const reviewData = await reviewApi.getByOrderId(orderId);
      if (reviewData) {
        // 处理评价图片数据
        let images = [];
        if (reviewData.images) {
          if (typeof reviewData.images === 'string') {
            try {
              images = JSON.parse(reviewData.images);
            } catch (e) {
              images = reviewData.images.split(',').filter(img => img.trim());
            }
          } else if (Array.isArray(reviewData.images)) {
            images = reviewData.images;
          }
        }

        // 格式化评价数据
        const formattedReviewData = {
          ...reviewData,
          createdAt: reviewData.createdAt ? formatNormalDate(reviewData.createdAt) : '',
          ratingStars: this.generateStars(reviewData.rating || 0),
          images: images,
        };

        this.setData({
          reviewData: formattedReviewData,
          hasReview: true,
        });
      } else {
        this.setData({
          reviewData: null,
          hasReview: false,
        });
      }
    } catch (error) {
      console.error('加载评价数据失败:', error);
      this.setData({
        reviewData: null,
        hasReview: false,
      });
    } finally {
      this.setData({ reviewLoading: false });
    }
  },

  // 生成星级评分显示
  generateStars(rating) {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    // 添加实心星星
    for (let i = 0; i < fullStars; i++) {
      stars.push('★');
    }

    // 添加半星（如果有）
    if (hasHalfStar) {
      stars.push('☆');
    }

    // 添加空星星
    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push('☆');
    }

    return stars.join('');
  },

  // 加载服务照片数据
  async loadServicePhotos(orderId) {
    this.setData({ servicePhotosLoading: true });

    try {
      const servicePhotos = await orderApi.getServicePhotos(orderId);
      if (servicePhotos) {
        // 确保照片数组存在
        const beforePhotos = servicePhotos.beforePhotos || [];
        const afterPhotos = servicePhotos.afterPhotos || [];

        // 只有当至少有一种类型的照片时才显示组件
        const hasPhotos = beforePhotos.length > 0 || afterPhotos.length > 0;

        this.setData({
          servicePhotos: {
            beforePhotos: beforePhotos,
            afterPhotos: afterPhotos,
          },
          hasServicePhotos: hasPhotos,
        });
      } else {
        this.setData({
          servicePhotos: null,
          hasServicePhotos: false,
        });
      }
    } catch (error) {
      console.error('加载服务照片失败:', error);
      this.setData({
        servicePhotos: null,
        hasServicePhotos: false,
      });
    } finally {
      this.setData({ servicePhotosLoading: false });
    }
  },

  // 预览评价图片
  previewImage(e) {
    const { current, urls } = e.currentTarget.dataset;
    wx.previewImage({
      current: current,
      urls: urls,
    });
  },

  // 通过API加载订单详情
  async loadOrderDetail() {
    wx.showLoading({ title: '加载中...' });
    try {
      // 首先尝试从本地存储获取（订单列表页面已经存储了数据）
      const info = wx.getStorageSync('orderInfo');

      if (info) {
        const formattedOrder = {
          ...info,
          orderId: info.id,
          orderNumber: info.sn,
          status: info.status,
          statusText: info.status,
          userRemark: info.orderDetails?.[0].userRemark,
          productName: info.orderDetails?.[0].service.serviceName,
          productImage: info.orderDetails?.[0].service.logo,
          petName: info.orderDetails?.[0].petName,
          userAdress: info.addressDetail + '(' + info.addressRemark + ')',
          quantity: 1,
          expectTime: formatNormalDate(info.serviceTime),
          serviceTime: info.serviceTime,
          extraServive: (info.orderDetails?.[0].additionalServices || []).map(v => v.name),
        };

        this.setOrderDetail(formattedOrder);
        return;
      }

      wx.showToast({
        title: '获取订单信息失败',
        icon: 'none',
      });
    } catch (error) {
      console.error('加载订单详情失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none',
      });
    } finally {
      wx.hideLoading();
    }
  },
  // 获取订单状态文字
  getOrderStatusText(status) {
    const statusMap = {
      unpaid: '待付款',
      paid: '待接单',
      completed: '进行中',
      review: '待评价',
    };
    return statusMap[status] || '未知状态';
  },
  // 切换更多操作菜单
  toggleMoreActions() {
    this.setData({
      showMoreActions: !this.data.showMoreActions,
    });
  },
  // 提交订单
  submitOrder() {
    // 验证表单信息
    if (!this.validateForm()) {
      wx.showToast({
        title: '请填写完整信息',
        icon: 'none',
      });
      return;
    }

    wx.showLoading({
      title: '提交订单中',
    });

    // 这里应该调用后端API提交订单
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '订单提交成功',
        icon: 'success',
        duration: 2000,
        success: () => {
          // 跳转到订单列表或详情页
          wx.navigateTo({
            url: '/pages/orderList/orderList',
          });
        },
      });
    }, 1500);
  },

  // 取消订单
  cancelOrder() {
    wx.navigateBack({
      delta: 1,
    });
  },

  // 表单验证
  validateForm() {
    const { customerName, customerPhone, customerAddress, quantity } = this.data;
    return customerName && customerPhone && customerAddress && quantity > 0;
  },

  // 联系客户
  contactCustomer() {
    const { orderDetail } = this.data;

    // 检查订单状态
    if (orderDetail.status !== '待服务') {
      wx.showToast({
        title: '只有待服务状态的订单才能联系客户',
        icon: 'none',
      });
      return;
    }

    const phoneNumber = orderDetail.customer?.phone || orderDetail.customer?.mobile;

    if (!phoneNumber) {
      wx.showToast({
        title: '客户手机号不存在',
        icon: 'none',
      });
      return;
    }

    wx.showModal({
      title: '联系客户',
      content: `确定要拨打客户电话 ${phoneNumber} 吗？`,
      success: res => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: phoneNumber,
            success: () => {},
            fail: err => {
              console.error('拨打电话失败:', err);
              wx.showToast({
                title: '拨打电话失败',
                icon: 'none',
              });
            },
          });
        }
      },
    });
  },

  // 修改上门时间
  reschedule() {
    const { orderDetail } = this.data;

    // 关闭更多操作菜单
    this.setData({
      showMoreActions: false,
    });

    // 检查订单状态
    if (orderDetail.status !== '待服务') {
      wx.showToast({
        title: '只有待服务状态的订单才能修改上门时间',
        icon: 'none',
      });
      return;
    }

    this.setData({
      showTimePicker: true,
      selectedTime: '',
    });
  },

  // 时间选择器确认
  onTimeSelected(e) {
    const selectedTime = e.detail;
    this.setData({
      selectedTime,
    });

    // 确认修改时间
    if (selectedTime) {
      this.confirmUpdateTime();
    }
  },

  // 确认修改时间
  async confirmUpdateTime() {
    if (!this.data.selectedTime) {
      wx.showToast({
        title: '请选择有效时间',
        icon: 'none',
      });
      return;
    }

    wx.showLoading({
      title: '修改中...',
    });

    try {
      const res = await orderApi.updateServiceTime(
        this.data.orderDetail.orderId || this.data.orderDetail.id,
        this.data.userInfo.id,
        new Date(this.data.selectedTime).toISOString()
      );

      if (res) {
        wx.showToast({
          title: '修改成功',
          icon: 'success',
        });

        // 更新本地订单数据
        const updatedOrderDetail = {
          ...this.data.orderDetail,
          serviceTime: formatNormalDate(this.data.selectedTime),
        };
        this.setData({
          orderDetail: updatedOrderDetail,
        });

        // 更新本地存储
        wx.setStorageSync('orderInfo', updatedOrderDetail);
      } else {
        wx.showToast({
          title: '修改失败',
          icon: 'error',
        });
      }
    } catch (error) {
      wx.showToast({
        title: '修改失败',
        icon: 'error',
      });
    } finally {
      wx.hideLoading();
      // 关闭时间选择器
      this.setData({
        showTimePicker: false,
        selectedTime: '',
      });
    }
  },

  // 取消时间选择
  onTimeCancel() {
    this.setData({
      showTimePicker: false,
      selectedTime: '',
    });
  },

  // 修改服务地址
  editServiceAddress() {
    const orderDetail = this.data.orderDetail;

    // 关闭更多操作菜单
    this.setData({
      showMoreActions: false,
    });

    // 检查订单状态权限
    if (!this.canEditAddress(orderDetail)) {
      wx.showToast({
        title: '当前订单状态不允许修改地址',
        icon: 'none',
      });
      return;
    }

    this.setData({
      showAddressEditor: true,
    });
  },

  // 检查是否可以修改地址
  canEditAddress(orderDetail) {
    const orderStatus = orderDetail.status;

    // 员工端权限：只能在出发前修改（待付款、待接单、待服务）
    const allowedStatuses = ['待付款', '待接单', '待服务'];
    return allowedStatuses.includes(orderStatus);
  },

  // 地址编辑确认
  async onAddressEditConfirm(e) {
    const addressData = e.detail;
    const orderDetail = this.data.orderDetail;

    wx.showLoading({
      title: '修改中...',
    });

    try {
      // 构建请求数据
      const requestData = {
        ...addressData,
        employeeId: this.data.userInfo.id,
        userType: 'employee', // 员工端
      };

      const result = await orderApi.updateServiceAddress(orderDetail.orderId || orderDetail.id, requestData);

      if (result) {
        wx.showToast({
          title: '地址修改成功',
          icon: 'success',
        });

        // 关闭编辑器
        this.setData({
          showAddressEditor: false,
        });

        // 更新本地订单数据
        const updatedOrderDetail = {
          ...orderDetail,
          address: addressData.address,
          addressDetail: addressData.addressDetail,
          addressRemark: addressData.addressRemark,
          latitude: addressData.latitude,
          longitude: addressData.longitude,
        };

        this.setData({
          orderDetail: updatedOrderDetail,
        });

        // 更新本地存储
        wx.setStorageSync('orderInfo', updatedOrderDetail);
      } else {
        wx.showToast({
          title: '修改失败',
          icon: 'error',
        });
      }
    } catch (error) {
      console.error('修改地址失败:', error);

      // 根据错误类型显示不同的提示信息
      let errorMessage = '修改失败';
      if (error && error.message) {
        if (error.message.includes('不允许修改')) {
          errorMessage = '当前订单状态不允许修改地址';
        } else if (error.message.includes('权限')) {
          errorMessage = '没有修改权限';
        } else if (error.message.includes('网络')) {
          errorMessage = '网络错误，请重试';
        }
      }

      wx.showToast({
        title: errorMessage,
        icon: 'none',
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 地址编辑取消
  onAddressEditCancel() {
    this.setData({
      showAddressEditor: false,
    });
  },

  // 关闭更多操作菜单
  closeMoreActions() {
    this.setData({
      showMoreActions: false,
    });
  },

  // 查看评价
  async viewReview() {
    const orderId = this.data.orderDetail.orderId || this.data.orderDetail.id;

    wx.showLoading({
      title: '加载中...',
    });

    try {
      const reviewData = await reviewApi.getByOrderId(orderId);
      if (reviewData) {
        // 将评价信息存储到本地存储，供评价详情页使用
        wx.setStorageSync('reviewInfo', reviewData);
        wx.navigateTo({
          url: `/pages/orders/reviewDetail/index?orderId=${orderId}`,
        });
      } else {
        wx.showToast({
          title: '暂无评价信息',
          icon: 'none',
        });
      }
    } catch (error) {
      console.error('获取评价信息失败:', error);
      wx.showToast({
        title: '获取评价失败',
        icon: 'error',
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 打开导航（组件事件处理）
  openNavigation(e) {
    const { address, remark, latitude, longitude } = e.detail;

    // 使用统一的地址工具
    AddressUtils.openNavigation({
      address,
      remark,
      latitude,
      longitude,
    });
  },

  // 确认追加服务（组件事件处理）
  confirmAdditionalService(e) {
    const { service } = e.detail;
    const serviceName = service.details?.[0]?.serviceName || service.serviceName || '追加服务';

    wx.showModal({
      title: '确认追加服务',
      content: `确定要确认客户申请的"${serviceName}"追加服务吗？`,
      success: async res => {
        if (res.confirm) {
          wx.showLoading({
            title: '处理中...',
          });

          try {
            const result = await orderApi.confirmAdditionalService(
              service.orderDetailId || this.data.orderDetail.orderDetails[0].id,
              service.id,
              this.data.userInfo.id
            );

            if (result) {
              wx.showToast({
                title: '确认成功',
                icon: 'success',
              });

              // 刷新追加服务列表（重新加载会自动分离待确认和其他状态）
              if (this.data.orderDetail.orderDetails && this.data.orderDetail.orderDetails.length > 0) {
                this.loadAllAdditionalServices(this.data.orderDetail.orderDetails[0].id);
              }

              // 通知订单列表页面刷新数据
              this.notifyOrderListRefresh();
            } else {
              wx.showToast({
                title: '确认失败',
                icon: 'error',
              });
            }
          } catch (error) {
            console.error('确认追加服务失败:', error);
            wx.showToast({
              title: '确认失败',
              icon: 'error',
            });
          } finally {
            wx.hideLoading();
          }
        }
      },
    });
  },

  // 拒绝追加服务（组件事件处理）
  rejectAdditionalService(e) {
    const { service } = e.detail;
    this.setData({
      currentAdditionalService: service,
      showRejectModal: true,
      rejectReason: '',
    });
  },

  // 输入拒绝原因
  onRejectReasonInput(e) {
    this.setData({
      rejectReason: e.detail.value,
    });
  },

  // 确认拒绝
  async confirmReject() {
    if (!this.data.rejectReason.trim()) {
      wx.showToast({
        title: '请输入拒绝原因',
        icon: 'none',
      });
      return;
    }

    wx.showLoading({
      title: '处理中...',
    });

    try {
      const service = this.data.currentAdditionalService;
      const result = await orderApi.rejectAdditionalService(
        service.orderDetailId || this.data.orderDetail.orderDetails[0].id,
        service.id,
        this.data.userInfo.id,
        this.data.rejectReason
      );

      if (result) {
        wx.showToast({
          title: '已拒绝',
          icon: 'success',
        });

        // 关闭拒绝模态框
        this.setData({
          showRejectModal: false,
          currentAdditionalService: null,
          rejectReason: '',
        });

        // 刷新追加服务列表（重新加载会自动分离待确认和其他状态）
        if (this.data.orderDetail.orderDetails && this.data.orderDetail.orderDetails.length > 0) {
          this.loadAllAdditionalServices(this.data.orderDetail.orderDetails[0].id);
        }

        // 通知订单列表页面刷新数据
        this.notifyOrderListRefresh();
      } else {
        wx.showToast({
          title: '拒绝失败',
          icon: 'error',
        });
      }
    } catch (error) {
      console.error('拒绝追加服务失败:', error);
      wx.showToast({
        title: '拒绝失败',
        icon: 'error',
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 取消拒绝
  cancelReject() {
    this.setData({
      showRejectModal: false,
      currentAdditionalService: null,
      rejectReason: '',
    });
  },

  // 删除追加服务（组件事件处理）
  deleteAdditionalService(e) {
    const { service } = e.detail;
    const serviceName = service.details?.[0]?.serviceName || service.serviceName || '追加服务';

    wx.showModal({
      title: '删除追加服务',
      content: `确定要删除"${serviceName}"追加服务吗？删除后无法恢复。`,
      confirmText: '删除',
      confirmColor: '#ff4444',
      cancelText: '取消',
      success: async res => {
        if (res.confirm) {
          wx.showLoading({
            title: '删除中...',
          });

          try {
            const result = await orderApi.deleteAdditionalService(
              service.orderDetailId || this.data.orderDetail.orderDetails[0].id,
              service.id,
              this.data.userInfo.id
            );

            if (result) {
              wx.showToast({
                title: '删除成功',
                icon: 'success',
              });

              // 刷新追加服务列表
              if (this.data.orderDetail.orderDetails && this.data.orderDetail.orderDetails.length > 0) {
                this.loadAllAdditionalServices(this.data.orderDetail.orderDetails[0].id);
              }

              // 通知订单列表页面刷新数据
              this.notifyOrderListRefresh();
            } else {
              wx.showToast({
                title: '删除失败',
                icon: 'error',
              });
            }
          } catch (error) {
            console.error('删除追加服务失败:', error);
            wx.showToast({
              title: '删除失败',
              icon: 'error',
            });
          } finally {
            wx.hideLoading();
          }
        }
      },
    });
  },

  // 特殊情况说明相关方法

  // 加载特殊情况说明
  async loadSpecialNote(orderId) {
    try {
      const noteData = await specialNoteApi.get(orderId);
      this.setData({
        specialNoteData: noteData,
      });
    } catch (error) {

      this.setData({
        specialNoteData: null,
      });
    }
  },

  // 显示特殊情况说明弹窗
  showSpecialNote() {
    const { orderDetail } = this.data;

    // 根据订单状态判断是否为只读模式
    const readonly = orderDetail.status === '已完成' || orderDetail.status === '已评价';

    this.setData({
      showSpecialNote: true,
      specialNoteReadonly: readonly,
    });
  },

  // 特殊情况说明确认提交
  async onSpecialNoteConfirm(e) {
    const { content, photoList } = e.detail;
    const { orderDetail, specialNoteData, userInfo } = this.data;
    const orderId = orderDetail.orderId || orderDetail.id;
    const employeeId = userInfo.id;

    wx.showLoading({
      title: '保存中...',
    });

    try {
      let result;
      if (specialNoteData) {
        // 更新已有的特殊情况说明
        result = await specialNoteApi.update(orderId, employeeId, content, photoList);
      } else {
        // 创建新的特殊情况说明
        result = await specialNoteApi.create(orderId, employeeId, content, photoList);
      }

      if (result) {
        wx.showToast({
          title: '保存成功',
          icon: 'success',
        });
        // 重新加载特殊情况说明数据
        this.loadSpecialNote(orderId);
        this.onSpecialNoteCancel();
      } else {
        wx.showToast({
          title: '保存失败',
          icon: 'error',
        });
      }
    } catch (error) {
      console.error('保存特殊情况说明失败:', error);
      wx.showToast({
        title: '保存失败',
        icon: 'error',
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 特殊情况说明取消
  onSpecialNoteCancel() {
    this.setData({
      showSpecialNote: false,
      specialNoteReadonly: false,
    });
  },

  // 删除特殊情况说明
  async onSpecialNoteDelete() {
    const { orderDetail, userInfo } = this.data;
    const orderId = orderDetail.orderId || orderDetail.id;
    const employeeId = userInfo.id;

    wx.showLoading({
      title: '删除中...',
    });

    try {
      const result = await specialNoteApi.delete(orderId, employeeId);
      if (result) {
        wx.showToast({
          title: '删除成功',
          icon: 'success',
        });
        // 重新加载特殊情况说明数据
        this.loadSpecialNote(orderId);
        this.onSpecialNoteCancel();
      } else {
        wx.showToast({
          title: '删除失败',
          icon: 'error',
        });
      }
    } catch (error) {
      console.error('删除特殊情况说明失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'error',
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 预览特殊情况说明图片
  previewSpecialNotePhoto(e) {
    const url = e.currentTarget.dataset.url;
    const urls = e.currentTarget.dataset.urls;

    wx.previewImage({
      current: url,
      urls: urls,
    });
  },

  // ==================== 服务时长统计相关方法 ====================

  // 格式化时长显示（友好格式）
  formatDuration(minutes) {
    // 确保最小值为0，防止显示负数
    const safeMinutes = Math.max(0, minutes || 0);

    if (safeMinutes < 1) {
      return '不到1分钟';
    }

    const hours = Math.floor(safeMinutes / 60);
    const mins = safeMinutes % 60;

    if (hours === 0) {
      return `${mins}分钟`;
    } else if (mins === 0) {
      return `${hours}小时`;
    } else {
      return `${hours}小时${mins}分钟`;
    }
  },

  // 启动实时计时器
  startRealtimeTimer() {
    // 清除已存在的计时器
    this.stopRealtimeTimer();

    // 每30秒更新一次实时用时
    this.realtimeTimer = setInterval(() => {
      this.updateRealtimeDuration();
    }, 30000); // 30秒更新一次
  },

  // 停止实时计时器
  stopRealtimeTimer() {
    if (this.realtimeTimer) {
      clearInterval(this.realtimeTimer);
      this.realtimeTimer = null;
    }
  },

  // 更新实时用时显示
  updateRealtimeDuration() {
    const { orderDetail, mainServiceRecords, additionalServiceRecords } = this.data;

    if (!orderDetail || orderDetail.status !== '服务中') {
      return;
    }

    let hasRunningService = false;

    // 更新主服务的实时用时
    if (orderDetail.orderDetails && orderDetail.orderDetails.length > 0) {
      orderDetail.orderDetails.forEach(item => {
        const record = mainServiceRecords.find(r => r.orderDetailId == item.id);
        if (record && record.isRunning) {
          hasRunningService = true;
          const currentTime = new Date();
          const startTime = new Date(record.startTime);
          const diffMinutes = Math.max(0, Math.floor((currentTime - startTime) / (1000 * 60)));

          // 更新显示的实时用时
          item.currentDuration = this.formatDuration(diffMinutes);
        }
      });
    }

    // 更新增项服务的实时用时
    const { allAdditionalServices = [] } = this.data;
    if (allAdditionalServices && allAdditionalServices.length > 0) {
      allAdditionalServices.forEach(item => {
        // 根据服务类型确定查找记录的方式
        let record = null;
        if (item.type === 'original') {
          // 主订单增项服务：使用服务ID查找
          record = additionalServiceRecords.find(
            r => r.recordType === 'additional_service' && r.additionalServiceId == item.id
          );
        } else if (item.type === 'additional') {
          // 追加服务订单：使用订单ID查找
          record = additionalServiceRecords.find(r => r.additionalServiceOrderId == item.id);
        }

        if (record && record.isRunning) {
          hasRunningService = true;
          const currentTime = new Date();
          const startTime = new Date(record.startTime);
          const diffMinutes = Math.max(0, Math.floor((currentTime - startTime) / (1000 * 60)));

          // 更新显示的实时用时
          item.currentDuration = this.formatDuration(diffMinutes);
        }
      });
    }

    // 如果有正在进行的服务，更新页面数据
    if (hasRunningService) {
      this.setData({
        orderDetail: orderDetail,
        allAdditionalServices: allAdditionalServices,
      });
    } else {
      // 没有正在进行的服务，停止计时器
      this.stopRealtimeTimer();
    }
  },

  // 加载订单服务状态（新接口）
  async loadOrderServiceStatus(orderId) {
    try {
      const result = await serviceDurationApi.getOrderServiceStatus(orderId);

      if (result) {
        // 处理主服务数据
        const mainServices = result.mainServices || [];
        const formattedMainServices = mainServices.map(service => ({
          ...service,
          serviceRecord: service.serviceRecord
            ? {
                ...service.serviceRecord,
                startTime: service.serviceRecord.startTime ? formatNormalDate(service.serviceRecord.startTime) : null,
                endTime: service.serviceRecord.endTime ? formatNormalDate(service.serviceRecord.endTime) : null,
                durationText: this.formatDuration(service.serviceRecord.duration),
                isRunning: !service.serviceRecord.endTime,
              }
            : null,
        }));

        // 处理增项服务数据
        const additionalServices = result.additionalServices || [];
        const formattedAdditionalServices = additionalServices.map(service => ({
          ...service,
          serviceRecord: service.serviceRecord
            ? {
                ...service.serviceRecord,
                startTime: service.serviceRecord.startTime ? formatNormalDate(service.serviceRecord.startTime) : null,
                endTime: service.serviceRecord.endTime ? formatNormalDate(service.serviceRecord.endTime) : null,
                durationText: this.formatDuration(service.serviceRecord.duration),
                isRunning: !service.serviceRecord.endTime,
              }
            : null,
        }));

        // 为了兼容原有的显示逻辑，也需要设置mainServiceRecords和additionalServiceRecords
        const mainServiceRecords = formattedMainServices
          .filter(service => service.serviceRecord)
          .map(service => ({
            ...service.serviceRecord,
            orderDetailId: service.orderDetailId, // 保持原始类型
            serviceId: service.serviceId,
            serviceName: service.serviceName,
            recordType: 'main_service',
          }));

        const additionalServiceRecords = formattedAdditionalServices
          .filter(service => service.serviceRecord)
          .map(service => ({
            ...service.serviceRecord,
            additionalServiceOrderId: service.additionalServiceOrderId, // 保持原始类型
            additionalServiceId: service.serviceId,
            serviceName: service.serviceName,
            recordType: 'additional_service',
          }));

        // 更新 allAdditionalServices 中的 durationStatus 字段
        const updatedAllAdditionalServices =
          this.data.allAdditionalServices?.map(service => {
            if (service.type === 'original') {
              // 尝试多种匹配方式
              const statusInfo = formattedAdditionalServices.find(
                statusItem =>
                  statusItem.serviceId == service.additionalServiceId ||
                  statusItem.additionalServiceOrderId == service.id ||
                  statusItem.serviceName === service.serviceName
              );
              if (statusInfo) {
                return {
                  ...service,
                  durationStatus: statusInfo.status,
                };
              }
            }
            return service;
          }) || [];

        this.setData({
          orderServiceStatus: result,
          mainServicesStatus: formattedMainServices,
          additionalServicesStatus: formattedAdditionalServices,
          serviceStatistics: result.serviceStatistics || {},
          serviceSummary: result.summary || {},
          // 兼容原有逻辑的数据
          mainServiceRecords: mainServiceRecords,
          additionalServiceRecords: additionalServiceRecords,
          // 更新包含最新状态的增项服务数据
          allAdditionalServices: updatedAllAdditionalServices,
        });

        return result;
      }
    } catch (error) {
      console.error('加载订单服务状态失败:', error);
      return null;
    }
  },

  // 加载服务时长记录
  async loadServiceDurationRecords(orderId, retryCount = 0) {
    // 防止重复请求
    if (this.data.loadingStates.serviceDurationRecords) {

      return;
    }

    try {
      // 设置加载状态
      this.setData({
        'loadingStates.serviceDurationRecords': true,
      });

      const result = await serviceDurationApi.getRecords(orderId);

      // 新接口返回格式：{ orderId, records, statistics }
      const records = result?.records || [];
      const statistics = result?.statistics || {};



      if (records && Array.isArray(records)) {
        // 格式化时长记录数据
        const formattedRecords = records.map(record => ({
          ...record,
          startTime: record.startTime ? formatNormalDate(record.startTime) : null,
          endTime: record.endTime ? formatNormalDate(record.endTime) : null,
          durationText: this.formatDuration(record.duration),
          isRunning: !record.endTime, // 没有结束时间表示正在进行中
          // 确保增项服务相关字段正确处理
          additionalServiceName: record.additionalServiceName || record.serviceName,
          // 处理关联对象
          employee: record.employee || {},
          service: record.service || {},
          orderDetail: record.orderDetail || {},
          additionalService: record.additionalService || {},
          additionalServiceOrder: record.additionalServiceOrder || {},
        }));

        // 分离主服务和增项服务记录
        const mainServiceRecords = formattedRecords.filter(record => record.recordType === 'main_service');
        const additionalServiceRecords = formattedRecords.filter(record => record.recordType === 'additional_service');



        // 为统计数据添加格式化文本
        const formattedStatistics = {
          ...statistics,
          totalDurationText: statistics.totalDuration ? this.formatDuration(statistics.totalDuration) : '0分钟',
          mainServiceDurationText: statistics.mainServiceDuration
            ? this.formatDuration(statistics.mainServiceDuration)
            : '0分钟',
          additionalServiceDurationText: statistics.additionalServiceDuration
            ? this.formatDuration(statistics.additionalServiceDuration)
            : '0分钟',
        };

        // 先设置数据到页面状态
        this.setData({
          serviceDurationRecords: formattedRecords,
          mainServiceRecords: mainServiceRecords,
          additionalServiceRecords: additionalServiceRecords,
          serviceDurationStatistics: formattedStatistics, // 包含格式化文本的统计信息
        });

        // 然后计算WXML需要的数据（传入最新的记录数据）
        const computedData = this.computeServiceDisplayData(mainServiceRecords, additionalServiceRecords);

        // 更新包含计算属性的数据
        this.setData({
          orderDetail: computedData.orderDetail, // 更新包含计算属性的订单详情
          allAdditionalServices: this.data.allAdditionalServices, // 确保增项服务数据也被更新
        });

        // 重置加载状态
        this.setData({
          'loadingStates.serviceDurationRecords': false,
        });

        // 如果增项服务已加载，立即重新计算以确保显示正确
        if (this.data.allAdditionalServices && this.data.allAdditionalServices.length > 0) {
          // 直接使用刚刚加载的最新数据重新计算
          const recomputedData = this.computeServiceDisplayData(mainServiceRecords, additionalServiceRecords);
          this.setData({
            orderDetail: recomputedData.orderDetail,
          });
        }

        // 如果是服务中状态但没有主服务记录，且重试次数少于2次，则1秒后重试
        if (this.data.orderDetail?.status === '服务中' && mainServiceRecords.length === 0 && retryCount < 2) {
          setTimeout(() => {
            this.loadServiceDurationRecords(orderId, retryCount + 1);
          }, 1000);
        }
      }
    } catch (error) {
      console.error('加载服务时长记录失败:', error);

      // 重置加载状态
      this.setData({
        'loadingStates.serviceDurationRecords': false,
      });

      // 如果是网络错误且重试次数少于2次，则重试
      if (retryCount < 2) {

        setTimeout(() => {
          this.loadServiceDurationRecords(orderId, retryCount + 1);
        }, 1000);
      }
    }
  },

  // 通知订单列表页面刷新数据
  notifyOrderListRefresh() {
    try {
      const pages = getCurrentPages();
      const prevPage = pages[pages.length - 2]; // 获取上一个页面

      if (prevPage && prevPage.route === 'pages/orders/index') {
        // 设置刷新标记
        prevPage.setData({
          needRefresh: true,
        });

        // 如果页面有刷新方法，直接调用
        if (typeof prevPage.resetAndReload === 'function') {
          prevPage.resetAndReload();
        }
      }
    } catch (error) {
      console.error('通知订单列表刷新失败:', error);
    }
  },

  // 开始服务时长统计
  async startServiceDuration(serviceType, serviceData) {
    const { orderDetail } = this.data;
    const orderId = orderDetail.orderId || orderDetail.id;

    try {
      const params = {
        orderId: orderId,
        recordType: serviceType, // 'main_service' 或 'additional_service'
        remark: `开始${serviceData.serviceName || serviceData.name}服务`,
      };

      // 根据服务类型设置不同的参数
      if (serviceType === 'main_service') {
        params.orderDetailId = orderDetail.orderDetails[0].id;
        params.serviceId = orderDetail.orderDetails[0].service.id;
      } else if (serviceType === 'additional_service') {
        params.additionalServiceOrderId = serviceData.id;
        params.additionalServiceId = serviceData.additionalServiceId;
      }

      const result = await serviceDurationApi.start(params);
      if (result) {
        wx.showToast({
          title: '开始计时',
          icon: 'success',
        });
        // 重新加载服务时长记录
        this.loadServiceDurationRecords(orderId);
      }
    } catch (error) {
      console.error('开始服务时长统计失败:', error);
      wx.showToast({
        title: '开始计时失败',
        icon: 'error',
      });
    }
  },

  // 结束服务时长统计（组件事件处理）
  async endServiceDuration(e) {
    const { recordId, serviceName } = e.detail;

    wx.showModal({
      title: '确认完成',
      content: `确定要完成"${serviceName}"服务吗？`,
      confirmText: '完成',
      cancelText: '取消',
      success: async res => {
        if (res.confirm) {
          wx.showLoading({
            title: '处理中...',
          });

          try {
            const params = {
              recordId: recordId,
              remark: `完成${serviceName}服务`,
            };

            const result = await serviceDurationApi.end(params);
            if (result) {
              wx.showToast({
                title: '服务已完成',
                icon: 'success',
              });

              // 重新加载服务时长记录和订单服务状态
              const { orderDetail } = this.data;
              const orderId = orderDetail.orderId || orderDetail.id;



              // 并行加载最新数据
              await Promise.all([this.loadServiceDurationRecords(orderId), this.loadOrderServiceStatus(orderId)]);

              // 重新加载增项服务数据（这里包含 durationStatus 字段）
              const orderDetailId = orderDetail?.orderDetails?.[0]?.id || orderDetail?.id;
              if (orderDetailId) {
                await this.loadAllAdditionalServices(orderDetailId);
              }
            }
          } catch (error) {
            console.error('结束服务时长统计失败:', error);
            wx.showToast({
              title: '结束计时失败',
              icon: 'error',
            });
          } finally {
            wx.hideLoading();
          }
        }
      },
    });
  },

  // 启动服务计时器（用于实时显示当前服务时长）
  startServiceTimer() {
    // 清除之前的计时器
    if (this.data.serviceDurationTimer) {
      clearInterval(this.data.serviceDurationTimer);
    }

    // 每分钟更新一次当前服务时长显示
    const timer = setInterval(() => {
      this.updateCurrentServiceDuration();
    }, 60000); // 60秒更新一次

    this.setData({
      serviceDurationTimer: timer,
    });
  },

  // 更新当前服务时长显示
  updateCurrentServiceDuration() {
    const { serviceDurationRecords } = this.data;
    if (serviceDurationRecords.length === 0) return;

    const now = new Date();
    const updatedRecords = serviceDurationRecords.map(record => {
      if (record.startTime && !record.endTime) {
        const startTime = new Date(record.startTime);
        const diffMinutes = Math.max(0, Math.floor((now - startTime) / (1000 * 60)));
        return {
          ...record,
          currentDuration: diffMinutes,
          currentDurationText: this.formatDuration(diffMinutes),
        };
      }
      return record;
    });

    // 重新分离主服务和增项服务记录
    const mainServiceRecords = updatedRecords.filter(record => record.recordType === 'main_service');
    const additionalServiceRecords = updatedRecords.filter(record => record.recordType === 'additional_service');

    this.setData({
      serviceDurationRecords: updatedRecords,
      mainServiceRecords: mainServiceRecords,
      additionalServiceRecords: additionalServiceRecords,
    });
  },

  // 切换服务时长统计显示
  toggleServiceDuration() {
    this.setData({
      showServiceDuration: !this.data.showServiceDuration,
    });
  },

  // 开始主服务时长统计（组件事件处理）
  async startMainService(e) {
    const { orderDetailId, serviceId, serviceName } = e.detail;
    const { orderDetail } = this.data;
    const orderId = orderDetail.orderId || orderDetail.id;

    try {
      const params = {
        orderId: orderId,
        orderDetailId: orderDetailId,
        recordType: 'main_service',
        serviceId: serviceId,
        remark: `开始${serviceName}服务`,
      };

      const result = await serviceDurationApi.start(params);
      if (result) {
        wx.showToast({
          title: '开始计时',
          icon: 'success',
        });

        // 重新加载相关数据
        await this.loadOrderServiceStatus(orderId);
        await this.loadServiceDurationRecords(orderId);

        // 重新加载增项服务数据（这里包含 durationStatus 字段）
        const orderDetailId = this.data.orderDetail?.orderDetails?.[0]?.id || this.data.orderDetail?.id;
        if (orderDetailId) {
          await this.loadAllAdditionalServices(orderDetailId);
        }

        // 刷新界面显示
        this.forceRefreshUI();

        // 启动实时计时器
        this.startRealtimeTimer();
      }
    } catch (error) {
      console.error('开始主服务时长统计失败:', error);
      wx.showToast({
        title: '开始计时失败',
        icon: 'error',
      });
    }
  },

  // 开始增项服务时长统计（组件事件处理）
  async startAdditionalService(e) {
    const { serviceType, serviceId, additionalServiceId, orderDetailId, serviceName } = e.detail;
    const { orderDetail, allAdditionalServices, additionalServiceRecords } = this.data;
    const orderId = orderDetail.orderId || orderDetail.id;

    // 先检查当前数据状态
    const currentService = allAdditionalServices.find(
      s => (s.type === 'original' && s.id == serviceId) || (s.type === 'additional' && s.id == serviceId)
    );

    // 检查服务当前状态，使用新的 durationStatus 字段判断
    if (serviceType === 'original' && currentService?.durationStatus) {
      // 主订单增项服务：使用 durationStatus 字段判断
      if (currentService.durationStatus === 'in_progress') {
        return;
      } else if (currentService.durationStatus === 'completed') {
        return;
      }
    } else {
      // 追加服务订单：使用旧逻辑检查记录
      let existingRecord = null;
      if (serviceType === 'additional') {
        existingRecord = additionalServiceRecords.find(r => r.additionalServiceOrderId == serviceId);
      }

      if (existingRecord && existingRecord.isRunning) {
        return;
      }
    }

    try {
      let params;

      // 根据服务类型设置不同的参数
      if (serviceType === 'original') {
        // 主订单中的增项服务
        params = {
          orderId: orderId,
          recordType: 'additional_service',
          orderDetailId: orderDetailId,
          additionalServiceId: additionalServiceId,
          remark: `开始${serviceName}增项服务`,
        };
      } else if (serviceType === 'additional') {
        // 追加服务中的增项服务
        params = {
          orderId: orderId,
          recordType: 'additional_service',
          additionalServiceOrderId: serviceId,
          additionalServiceId: additionalServiceId,
          remark: `开始${serviceName}增项服务`,
        };
      } else {
        throw new Error(`未知的服务类型: ${serviceType}`);
      }

      const result = await serviceDurationApi.start(params);

      if (result) {
        wx.showToast({
          title: '开始计时',
          icon: 'success',
        });

        // 重新加载相关数据
        await this.loadOrderServiceStatus(orderId);
        await this.loadServiceDurationRecords(orderId);

        // 重新加载增项服务数据（这里包含 durationStatus 字段）
        const orderDetailId = this.data.orderDetail?.orderDetails?.[0]?.id || this.data.orderDetail?.id;
        if (orderDetailId) {
          await this.loadAllAdditionalServices(orderDetailId);
        }

        // 刷新界面显示
        this.forceRefreshUI();

        // 启动实时计时器
        this.startRealtimeTimer();
      }
    } catch (error) {
      console.error('开始增项服务时长统计失败:', error);

      // 检查是否是"已经开始"的错误
      if (error.message && error.message.includes('已经开始')) {
        // 刷新数据以同步最新状态
        await this.loadOrderServiceStatus(orderId);
        await this.loadServiceDurationRecords(orderId);

        // 重新加载增项服务数据
        const orderDetailId = this.data.orderDetail?.orderDetails?.[0]?.id || this.data.orderDetail?.id;
        if (orderDetailId) {
          await this.loadAllAdditionalServices(orderDetailId);
        }

        this.forceRefreshUI();
      } else {
        wx.showToast({
          title: error.message || '开始计时失败',
          icon: 'error',
        });
      }
    }
  },

  // 强制刷新UI
  forceRefreshUI() {
    setTimeout(() => {


      // computeServiceDisplayData 会内部调用 setData 更新 allAdditionalServices
      const recomputedData = this.computeServiceDisplayData(
        this.data.mainServiceRecords,
        this.data.additionalServiceRecords
      );

      // 再次更新 orderDetail
      this.setData({
        orderDetail: recomputedData.orderDetail,
      });
    }, 200);
  },

  // 预计算WXML需要的显示数据
  computeServiceDisplayData(mainServiceRecords = null, additionalServiceRecords = null) {
    const { orderDetail } = this.data;

    // 如果没有传入参数，则从页面状态获取
    const finalMainServiceRecords = mainServiceRecords || this.data.mainServiceRecords || [];
    const finalAdditionalServiceRecords = additionalServiceRecords || this.data.additionalServiceRecords || [];

    // 为每个主服务添加计算好的显示属性
    if (orderDetail.orderDetails && orderDetail.orderDetails.length > 0) {
      orderDetail.orderDetails.forEach(item => {
        const orderDetailId = item.id;
        const record = finalMainServiceRecords.find(r => r.orderDetailId == orderDetailId);

        // 添加显示属性到item对象
        // 直接使用 record 字段判断状态（基于开始时间和结束时间）
        if (!record) {
          // 没有记录：未开始
          item.showStartBtn = true;
          item.showEndBtn = false;
          item.serviceStatus = 'not_started';
          item.serviceStatusText = '未开始';
        } else if (record.startTime && !record.endTime) {
          // 有开始时间，无结束时间：进行中
          item.showStartBtn = false;
          item.showEndBtn = true;
          item.serviceStatus = 'running';
          item.serviceStatusText = '进行中';
        } else if (record.startTime && record.endTime) {
          // 有开始时间和结束时间：已完成
          item.showStartBtn = false;
          item.showEndBtn = false;
          item.serviceStatus = 'completed';
          item.serviceStatusText = '已完成';
        } else {
          // 异常情况：默认为未开始
          item.showStartBtn = true;
          item.showEndBtn = false;
          item.serviceStatus = 'not_started';
          item.serviceStatusText = '未开始';
        }

        // 添加时间信息
        if (record) {
          item.startTime = record.startTime;
          item.recordId = record.id;

          if (record.startTime && !record.endTime) {
            // 正在进行的服务，计算实时用时
            const currentTime = new Date();
            const startTime = new Date(record.startTime);
            const diffMinutes = Math.max(0, Math.floor((currentTime - startTime) / (1000 * 60)));
            item.currentDuration = this.formatDuration(diffMinutes);
            item.duration = null;
          } else if (record.startTime && record.endTime) {
            // 已完成的服务，显示总用时
            item.duration = record.durationText;
            item.currentDuration = null;
          } else {
            // 未开始的服务
            item.duration = null;
            item.currentDuration = null;
          }
        } else {
          item.startTime = null;
          item.duration = null;
          item.currentDuration = null;
          item.recordId = null;
        }
      });
    }

    // 为每个增项服务添加计算好的显示属性
    const { allAdditionalServices = [] } = this.data;
    const newAllAdditionalServices = allAdditionalServices.map(item => {
      // 创建新的 item 对象，避免修改原对象
      const newItem = { ...item };
      let record = null;
      if (item.type === 'original') {
        record = finalAdditionalServiceRecords.find(
          r => r.recordType === 'additional_service' && r.additionalServiceId == item.additionalServiceId
        );
      } else if (item.type === 'additional') {
        record = finalAdditionalServiceRecords.find(r => r.additionalServiceOrderId == item.id);

      }

      // 修正按钮显示逻辑：
      // - 优先使用后台返回的 durationStatus 字段
      // - not_started 且已付款：显示"开始"按钮
      // - in_progress：显示"完成"按钮
      // - completed：不显示按钮
      // - 未付款：不显示操作按钮
      const isPaid = this.isAdditionalServicePaid(item);

      // 优先使用后台返回的 durationStatus 字段（仅主订单增项服务有此字段）
      const durationStatus = item.durationStatus;

      if (durationStatus && item.type === 'original') {
        // 使用新的 durationStatus 字段
        newItem.showStartBtn = durationStatus === 'not_started' && isPaid;
        newItem.showEndBtn = durationStatus === 'in_progress';
        newItem.serviceStatus =
          durationStatus === 'not_started' ? 'not_started' : durationStatus === 'in_progress' ? 'running' : 'completed';
        newItem.serviceStatusText =
          durationStatus === 'not_started' ? '未开始' : durationStatus === 'in_progress' ? '进行中' : '已完成';

        // 处理时长显示（新逻辑）
        if (durationStatus === 'in_progress') {
          // 进行中：显示实时用时
          if (record && record.isRunning) {
            const currentTime = new Date();
            const startTime = new Date(record.startTime);
            const diffMinutes = Math.max(0, Math.floor((currentTime - startTime) / (1000 * 60)));
            newItem.currentDuration = this.formatDuration(diffMinutes);
            newItem.duration = null;
            newItem.startTime = record.startTime;
            newItem.recordId = record.id;
          }
        } else if (durationStatus === 'completed') {
          // 已完成：显示总用时
          if (record && !record.isRunning) {
            // 使用实际服务时长记录
            newItem.duration = record.durationText;
            newItem.currentDuration = null;
            newItem.startTime = record.startTime;
            newItem.recordId = record.id;
          } else {
            // 没有实际记录时不显示时长信息
            newItem.duration = null;
            newItem.currentDuration = null;
            newItem.startTime = null;
            newItem.recordId = null;
          }
        } else {
          // 未开始：清空时长信息
          newItem.startTime = null;
          newItem.duration = null;
          newItem.currentDuration = null;
          newItem.recordId = null;
        }
      } else {
        // 统一逻辑：基于 record 字段判断状态（适用于追加服务增项）
        if (!record) {
          // 没有记录：未开始
          newItem.showStartBtn = isPaid;
          newItem.showEndBtn = false;
          newItem.serviceStatus = 'not_started';
          newItem.serviceStatusText = '未开始';
          newItem.startTime = null;
          newItem.duration = null;
          newItem.currentDuration = null;
          newItem.recordId = null;
        } else if (record.startTime && !record.endTime) {
          // 有开始时间，无结束时间：进行中
          newItem.showStartBtn = false;
          newItem.showEndBtn = true;
          newItem.serviceStatus = 'running';
          newItem.serviceStatusText = '进行中';
          newItem.startTime = record.startTime;
          newItem.recordId = record.id;
          // 计算实时用时
          const currentTime = new Date();
          const startTime = new Date(record.startTime);
          const diffMinutes = Math.max(0, Math.floor((currentTime - startTime) / (1000 * 60)));
          newItem.currentDuration = this.formatDuration(diffMinutes);
          newItem.duration = null;
        } else if (record.startTime && record.endTime) {
          // 有开始时间和结束时间：已完成
          newItem.showStartBtn = false;
          newItem.showEndBtn = false;
          newItem.serviceStatus = 'completed';
          newItem.serviceStatusText = '已完成';
          newItem.startTime = record.startTime;
          newItem.recordId = record.id;
          newItem.duration = record.durationText;
          newItem.currentDuration = null;
        } else {
          // 异常情况：默认为未开始
          newItem.showStartBtn = isPaid;
          newItem.showEndBtn = false;
          newItem.serviceStatus = 'not_started';
          newItem.serviceStatusText = '未开始';
          newItem.startTime = null;
          newItem.duration = null;
          newItem.currentDuration = null;
          newItem.recordId = null;
        }
      }

      // 更新操作按钮显示状态：需要统计时长、订单在服务中且已付款
      newItem.showDurationActions = newItem.needDurationTracking && orderDetail.status === '服务中' && isPaid;

      // 删除按钮显示状态：只有未付款的追加服务可以删除
      newItem.showDeleteBtn = this.canDeleteAdditionalService(newItem) && orderDetail.status === '服务中';

      return newItem;
    });

    // 更新数据
    this.setData({
      allAdditionalServices: newAllAdditionalServices,
    });

    return { orderDetail };
  },

  // 获取主服务记录
  getMainServiceRecord(orderDetailId) {
    const { mainServiceRecords } = this.data;
    if (!mainServiceRecords || mainServiceRecords.length === 0) {
      return null;
    }
    return mainServiceRecords.find(record => record.orderDetailId == orderDetailId);
  },

  // 获取已完成服务数量
  getCompletedServicesCount() {
    const { serviceDurationRecords } = this.data;
    return serviceDurationRecords.filter(record => !record.isRunning).length;
  },

  // 获取总用时
  getTotalDuration() {
    const { serviceDurationRecords } = this.data;
    const totalMinutes = serviceDurationRecords
      .filter(record => !record.isRunning && record.duration)
      .reduce((total, record) => total + record.duration, 0);
    return this.formatDuration(totalMinutes);
  },







  // 手动完成整体服务（保留原有方法）
  async completeOverallService() {
    const { orderDetail, userInfo } = this.data;
    const orderId = orderDetail.orderId || orderDetail.id;

    wx.showModal({
      title: '确认完成服务',
      content: '确定要完成整体服务吗？',
      success: async res => {
        if (res.confirm) {
          wx.showLoading({
            title: '处理中...',
          });

          try {
            // 调用完成服务接口
            const result = await orderApi.complete(orderId, userInfo.id);
            if (result) {
              wx.showToast({
                title: '服务已完成',
                icon: 'success',
              });

              // 通知订单列表页面刷新数据
              this.notifyOrderListRefresh();

              // 返回订单列表并刷新
              setTimeout(() => {
                wx.navigateBack();
              }, 1500);
            } else {
              // 不显示通用错误信息，因为 analysisRes 已经显示了具体错误信息
            }
          } catch (error) {
            console.error('完成服务失败:', error);
            // 不显示通用错误信息，因为 analysisRes 已经显示了具体错误信息
            // 如果是网络错误等非业务错误，才显示通用提示
            if (error && error.errMsg && error.errMsg.includes('request:fail')) {
              wx.showToast({
                title: '网络错误，请重试',
                icon: 'error',
              });
            }
          } finally {
            wx.hideLoading();
          }
        }
      },
    });
  },

  // 页面卸载时清除计时器
  onUnload() {
    // 清除旧的计时器
    if (this.data.serviceDurationTimer) {
      clearInterval(this.data.serviceDurationTimer);
    }

    // 清除实时计时器
    this.stopRealtimeTimer();
  },
});
